// webauthn.ts
import {
    startRegistration,
    startAuthentication,
    browserSupportsWebAuthn,
} from '@simplewebauthn/browser';

import {useLogins} from 'stores/logins';
import {useAuth} from 'stores/auth';

export function isWebAuthnSupported() {
    return browserSupportsWebAuthn();
}

export const getLoginAttempt = () => {
    return {loginAttempts: {value: new Date(), $position: 0, $slice: 20}}
}

import {DomainHandler} from 'src/utils/domain-handler';

export const getRpID = () => {
    const currentHostname = window.location.hostname;

    // Handle localhost variations
    if (currentHostname === 'localhost' || currentHostname === '127.0.0.1' || currentHostname === '[::1]') {
        return 'localhost';
    }

    // Handle IP addresses
    if (/^\d{1,3}(\.\d{1,3}){3}$/.test(currentHostname)) {
        return currentHostname;
    }

    // For regular domains, use the domain parsing logic
    const domainUtils = new DomainHandler();
    const domain = domainUtils.splitDomain(window.location.href);
    const res = `${domain.domain}`;

    // Additional check for localhost in parsed domain
    if (res.includes('localhost')) return 'localhost';

    return res;
}
/**
 * Register a new passkey for a user.
 * Calls your `webauthn` service over the socket:
 *  - create({ action: 'registrationOptions', ... })
 *  - startRegistration(options)
 *  - create({ action: 'registrationVerify', ... })
 */
export async function registerPasskey(
    opts: { login: any;  usernameField?: string; displayName?: string }
): Promise<{ success: boolean; message: string, login: any }> {
    if (!browserSupportsWebAuthn()) {
        return {login: undefined, success: false, message: 'Your browser does not support WebAuthn'};
    }

    try {
        const rpID = getRpID();
        const args:any = [{...opts.login}, {
            query: {
                loginOptions: {
                    usernameField: opts.usernameField,
                    rpID,
                    method: 'webauthn',
                    authAction: 'register'
                }
            }
        }]
        let method = 'create';
        if (opts.login._id) {
            args[0] = {$push: getLoginAttempt()}
            method = 'patch';
            args.unshift(opts.login._id as any);
        }
        // 1) Get options from server
        const login: any = await useLogins()[method](...args)

        // 2) Browser ceremony
        const credential = await startRegistration(login._fastjoin.webauthnOptions);

        // 3) Verify & persist authenticator on server
        await useAuth().authenticate({
            strategy: 'webauthn',
            credential,
            email: opts.login.email,
            register: true,
            rpID
        } as any)
            .catch(err => {
                console.error(`Error registering passkey: ${err.message}`)
                return {login: undefined, success: false, message: `Failed to register passkey: ${err.message}`};
            })

        return {login, success: true, message: 'Passkey created'};
    } catch (err: any) {
        // Common: NotAllowedError if user cancels, InvalidStateError if already registered, etc.
        return {login: undefined, success: false, message: err?.message ?? 'Registration error'};
    }
}

/**
 * Authenticate with a passkey.
 *  - create({ action: 'authenticationOptions', ... })
 *  - startAuthentication(options)
 *  - authentication.create({ strategy: 'webauthn', credential })
 *
 * If you omit loginId, this will attempt **usernameless** sign-in.
 */
export async function loginWithPasskey(
    opts: {
        login: any,
        usernameField?: string
    },
): Promise<{ login: any, success: boolean; message: string; authResult?: any }> {
    if (!browserSupportsWebAuthn()) {
        return {login: undefined, success: false, message: 'Your browser does not support WebAuthn'};
    }

    try {
        const rpID = getRpID();
        // 1) Get options (loginId optional for usernameless)
        const login: any = await useLogins().patch(opts.login._id, {
            $push: getLoginAttempt()
        }, {
            query: {
                loginOptions: {
                    usernameField: opts.usernameField,
                    rpID,
                    method: 'webauthn',
                    authAction: 'authenticate'
                    // Note: credential is NOT passed here - it will be generated by the browser
                    // and sent later during the authentication verification step
                }
            }
        }, { special_change: '*' });

        console.log('options', rpID, login._fastjoin.webauthnOptions);
        console.log('Current hostname:', window.location.hostname);
        console.log('Current origin:', window.location.origin);
        console.log('Computed rpID:', rpID);
        console.log('Options rpId:', login._fastjoin.webauthnOptions?.rpId);

        // Validate that the RP ID matches between client and server
        if (login._fastjoin.webauthnOptions?.rpId && login._fastjoin.webauthnOptions.rpId !== rpID) {
            console.warn(`RP ID mismatch: client computed '${rpID}' but server provided '${login._fastjoin.webauthnOptions.rpId}'`);
        }

        // 2) Browser ceremony
        const credential = await startAuthentication({
            optionsJSON: login._fastjoin.webauthnOptions,
            // useBrowserAutofill: true
        });

        // 3) Ask Feathers to issue JWT via your `webauthn` strategy
        await useAuth().authenticate({
            strategy: 'webauthn',
            credential,
            register: false,
            email: opts.login.email,
            rpID
        } as any)
            .catch(err => {
                console.error(`Error authenticating passkey: ${err.message}`)
                return {login: undefined, success: false, message: `Failed to authenticate passkey: ${err.message}`};
            })

        return {success: true, message: 'Signed in', login};
    } catch (err: any) {
        return {login: undefined, success: false, message: err?.message ?? 'Authentication error'};
    }
}
