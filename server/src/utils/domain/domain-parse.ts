import crypto from 'crypto';
import dns, {LookupAddress} from 'dns';
const {lookup} = dns.promises;
import cc from './check-context.js';
import {_get, _set} from '../dash-utils.js';
import {HookContext} from '../../declarations.js'

export const checkContext = cc;
export interface ParsedUrl {
  protocol: string;
  auth: { username?: string; password?: string };
  host: string;
  port: string | undefined;
  hostname: string | undefined;
  subdomain: string | undefined;
  domain: string;
  rootDomain: string | undefined;
  ip: string | undefined;
  ips: any[] | undefined; // Adjust type based on your lookup return type
  fqdn: string | undefined;
  hash: string;
  search: string;
  pathname: string;
  origin: string;
  href: string;
  isLocalhost: boolean;
  isIP: boolean;
}

export const urlParser = async (href: string): Promise<ParsedUrl> => {
  // Handle localhost by adding default protocol if none specified
  let normalizedHref = href;
  if (!href.match(/^([a-z]+:)?\/\//i)) {
    normalizedHref = `http://${href}`;
  }

  let url: URL;
  try {
    url = new URL(normalizedHref);
  } catch (error) {
    throw new Error(`Invalid URL: ${href}`);
  }

  const isLocalhost = url.hostname === 'localhost' || url.hostname === '127.0.0.1' || url.hostname === '[::1]' || url.origin.includes('localhost:');
  const isIP = !isLocalhost && url.hostname.split('.').every(val => Number.isFinite(+val) && !isNaN(parseInt(val)));

  return {
    isLocalhost,
    isIP,
    protocol: url.protocol.replace(/:/gi, ''),
    auth: {
      username: url.username === '' ? undefined : url.username,
      password: url.password === '' ? undefined : url.password,
    },
    host: url.host,
    port: (() => {
      if (url.port !== '' && url.port !== null) {
        return url.port;
      }
      switch (url.protocol.replace(/:/gi, '')) {
        case 'https':
          return '443';
        case 'http':
          return '80';
        default:
          return undefined;
      }
    })(),
    hostname: isLocalhost ? url.hostname : 'localhost',
    subdomain: (() => {
      if(isLocalhost) return url.hostname.split('.').length > 1 ? url.hostname.split('.')[0] : undefined;
      if (isIP) return undefined;
      const parts = url.hostname.split('.');
      if (parts.length > 2) {
        return parts[0];
      }
      return undefined;
    })(),
    domain: (() => {
      if (isLocalhost) return 'localhost';
      if (isIP) return url.hostname;
      const parts = url.hostname.split('.');
      if (parts.length >= 2) {
        return parts.slice(-2).join('.');
      }
      return url.hostname;
    })(),
    rootDomain: (() => {
      if (isLocalhost) return 'localhost';
      if (isIP) return undefined;
      const parts = url.hostname.split('.');
      return parts.length > 1 ? parts[parts.length - 1] : url.hostname;
    })(),
    ip: await (async () => {
      if (isLocalhost) return '127.0.0.1';
      try {
        return (await lookup(url.hostname, { family: 4 })).address;
      } catch {
        return undefined;
      }
    })(),
    ips: await (async () => {
      if (isLocalhost) return [{ address: '127.0.0.1', family: 'IPv4' }, { address: '::1', family: 'IPv6' }];
      try {
        return await lookup(url.hostname, { all: true });
      } catch {
        return undefined;
      }
    })(),
    fqdn: (() => {
      if (isLocalhost) return 'localhost';
      if (isIP) return url.hostname;
      const parts = url.hostname.split('.');
      if (parts.length >= 2) {
        return parts.slice(-2).join('.');
      }
      return url.hostname;
    })(),
    hash: crypto.createHash('md5').update(url.href).digest('hex'),
    search: url.search,
    pathname: url.pathname,
    origin: url.origin,
    href: url.href,
  };
};



// eslint-disable-next-line no-unused-vars
export const setUrlConfigParams = (options = {}) => {
  return async (context:HookContext):Promise<HookContext> => {
    cc(context, 'before', [], 'setUrlConfigParams');

    if (!_get(context, 'params.core')) {
      let url = _get(context, 'params.originalOrigin', _get(context, 'params.headers.origin'));
      if (url) {
        _set(context, 'params.originalOrigin', url);

        let urlConfig = await urlParser(url);
        if (urlConfig) {
          _set(context, 'params.urlConfig', urlConfig);
        }
      }
    }
    return context;
  };
};
